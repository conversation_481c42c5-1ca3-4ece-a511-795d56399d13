/**
 * 销售记录数据初始化器
 * 用于初始化销售记录系统和数据迁移
 */

import SalesManager from './salesManager.js'

class SalesDataInitializer {
	constructor() {
		this.salesManager = new SalesManager()
	}

	/**
	 * 初始化销售记录系统
	 * @returns {Object} 初始化结果
	 */
	async initializeSalesSystem() {
		try {
			console.log('开始初始化销售记录系统...')

			// 1. 检查销售记录表是否存在数据
			const existingSalesRecords = uni.getStorageSync('sales_records') || []
			console.log('现有销售记录数量:', existingSalesRecords.length)

			// 2. 如果没有销售记录，为现有的采茶工作记录创建默认销售记录
			if (existingSalesRecords.length === 0) {
				const result = await this.createDefaultSalesRecordsFromWorkRecords()
				console.log('默认销售记录创建结果:', result)
			}

			// 3. 验证数据完整性
			const validationResult = this.salesManager.validateDataIntegrity()
			console.log('数据完整性验证结果:', validationResult)

			// 4. 初始化应用配置
			this.initializeAppConfig()

			return {
				success: true,
				message: '销售记录系统初始化成功',
				data: {
					existingRecords: existingSalesRecords.length,
					validation: validationResult
				}
			}
		} catch (error) {
			console.error('初始化销售记录系统失败:', error)
			return {
				success: false,
				message: '初始化失败: ' + error.message
			}
		}
	}

	/**
	 * 为现有工作记录创建默认销售记录
	 * @returns {Object} 创建结果
	 */
	async createDefaultSalesRecordsFromWorkRecords() {
		try {
			const workRecords = uni.getStorageSync('workRecords') || []
			const teaPickingRecords = workRecords.filter(record => record.work_mode === 'tea_picking')
			
			console.log('找到采茶工作记录数量:', teaPickingRecords.length)

			const results = []
			const errors = []

			for (const workRecord of teaPickingRecords) {
				try {
					const result = await this.salesManager.createDefaultSalesRecordFromWorkRecord(workRecord)
					if (result.success) {
						results.push(result.data)
					} else {
						errors.push({
							workRecordId: workRecord.id,
							error: result.message
						})
					}
				} catch (error) {
					errors.push({
						workRecordId: workRecord.id,
						error: error.message
					})
				}
			}

			return {
				success: errors.length === 0,
				created: results.length,
				errors: errors.length,
				message: `成功创建 ${results.length} 条默认销售记录，失败 ${errors.length} 条`
			}
		} catch (error) {
			console.error('创建默认销售记录失败:', error)
			return {
				success: false,
				created: 0,
				errors: 1,
				message: '创建失败: ' + error.message
			}
		}
	}





	/**
	 * 验证数据完整性
	 * @returns {Object} 验证结果
	 */
	validateDataIntegrity() {
		return this.salesManager.validateDataIntegrity()
	}



	/**
	 * 初始化应用配置
	 */
	initializeAppConfig() {
		try {
			const existingConfig = uni.getStorageSync('app_config') || {}
			const defaultConfig = {
				salesRecordEnabled: true,
				autoCreateSalesRecord: true,
				defaultSellingPrice: 3.0,
				maxLocalRecords: 10000,
				syncInterval: 300000, // 5分钟
				...existingConfig
			}

			uni.setStorageSync('app_config', defaultConfig)
			console.log('应用配置初始化完成')
		} catch (error) {
			console.error('初始化应用配置失败:', error)
		}
	}


}

export default SalesDataInitializer
